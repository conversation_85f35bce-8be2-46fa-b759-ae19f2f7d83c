#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_pdf_view.py
<AUTHOR> JT_DA
@Date     : 2025/07/22
@File_Desc: 调解协议PDF生成视图
"""

import logging
from rest_framework.generics import GenericAPIView
from rest_framework import serializers

from apps.mediation_management.models import MediationCase
from apps.mediation_management.tasks import generate_mediation_agreement_pdf
from utils.ajax_result import AjaxResult
from utils.file_security_helper import FileSecurityHelper

# 获取日志记录器
logger = logging.getLogger(__name__)


class MediationAgreementPDFSerializer(serializers.Serializer):
    """
    调解协议PDF生成接口的序列化器，用于API文档生成。
    """
    pass


class MediationAgreementPDFView(GenericAPIView):
    """
    调解协议PDF生成和下载视图

    提供调解协议PDF文件的生成和安全下载功能。该视图通过调解案件ID和调解案件号进行双重校验，
    确保数据安全性，生成包含调解信息、调解方案和电子签名的标准化PDF协议文件。
    生成的PDF文件将存储到调解案件的mediation_agreement字段中，并提供安全的下载链接。
    """

    # 配置序列化器类用于API文档生成
    serializer_class = MediationAgreementPDFSerializer

    # 配置权限类：需要用户认证
    # permission_classes = [MyPermission]  # 可根据需要调整权限

    def get(self, request, case_number):
        """
        生成调解协议PDF文件并提供下载链接

        根据调解案件号生成包含调解信息、调解方案和电子签名的
        标准化PDF协议文件。生成的PDF文件将存储到调解案件对象中，并返回安全的下载链接。

        **请求参数：**

        **路径参数：**
        - case_number (字符串, 必需): 调解案件号，通过URL路径参数传递

        **请求数据示例：**
        ```
        GET /mediation_management/mediation_case/wechat/GZTJ20250801ABC123/agreement_pdf/
        ```

        **响应数据结构：**

        成功响应：
        ```json
        {
            "code": 200,
            "msg": "PDF生成成功",
            "state": "success",
            "data": {
                "download_url": "/user/files/download/550e8400-e29b-41d4-a716-446655440000/",
                "file_name": "调解协议_GZTJ20250801ABC123.pdf",
                "file_size": 1024000
            }
        }
        ```

        错误响应：
        ```json
        {
            "code": 404,
            "msg": "调解案件不存在或案件号不匹配",
            "state": "fail"
        }
        ```
        ```json
        {
            "code": 400,
            "msg": "调解案件缺少必要的配置信息",
            "state": "fail"
        }
        ```
        """
        try:
            # 记录请求日志
            logger.info(f"开始生成调解协议PDF，案件号: {case_number}")

            # 通过案件号查询调解案件
            try:
                mediation_case = MediationCase.objects.get(case_number=case_number)
            except MediationCase.DoesNotExist:
                logger.warning(f"调解案件不存在，案件号: {case_number}")
                return AjaxResult.not_found(msg="调解案件不存在")

            # 调用封装的PDF生成函数
            success, case_file, error_msg = generate_mediation_agreement_pdf(mediation_case)

            if not success:
                logger.error(f"PDF生成失败，案件号: {case_number}, 错误: {error_msg}")
                return AjaxResult.fail(msg=error_msg or "PDF生成失败")

            # 使用FileSecurityHelper生成安全下载链接
            download_url = FileSecurityHelper.generate_secure_download_url(case_file)
            if not download_url:
                logger.warning(f"无法生成安全下载链接，案件号: {case_number}")
                return AjaxResult.fail(msg="无法生成安全下载链接")

            # 构建响应数据
            file_name = f"调解协议_{case_number}.pdf"
            response_data = {
                "download_url": download_url,
                "file_name": file_name,
                "file_size": case_file.file.size if case_file.file else 0,
                "secure_token": str(case_file.secure_token) if case_file.secure_token else None,
            }

            logger.info(f"调解协议PDF生成成功，案件号: {case_number}, 文件大小: {case_file.file.size if case_file.file else 0} bytes")
            return AjaxResult.success(msg="PDF生成成功", data=response_data)

        except serializers.ValidationError as e:
            # 参数验证失败
            error_messages = []
            if hasattr(e, "detail"):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = "; ".join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except Exception as e:
            logger.error(f"生成调解协议PDF时发生异常: {str(e)}")
            return AjaxResult.fail(msg="PDF生成失败")
